# 🚀 Быстрый старт NFT Portals Bot

## 📦 Установка

1. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

2. **Настройте конфигурацию (опционально):**
```bash
cp .env.example .env
# Отредактируйте .env с вашими API_ID и API_HASH
```

## 🔑 Получение токена

1. Откройте [Telegram Web](https://web.telegram.org)
2. Нажмите **F12** → вкладка **Network**
3. Откройте бота **@portals** → **Market**
4. Найдите запрос к `portals-market.com`
5. Скопируйте значение **Authorization** из Headers

## 🎯 Запуск

### Простой способ (рекомендуется):
```bash
python bot_with_token.py
```

### Тестирование:
```bash
python manual_test.py
```

## 💡 Возможности

- 🔍 **Поиск NFT** по коллекции, модели, фону
- 💰 **Самые дешевые** NFT
- 💎 **Самые дорогие** NFT  
- 🆕 **Последние** выставленные NFT
- 📊 **Поиск по цене** в диапазоне
- 🔄 **Мониторинг** новых NFT

## 📋 Примеры поиска

### Поиск дешевых NFT с черным фоном:
1. Запустите бот
2. Выберите "Самые дешевые NFT"
3. Укажите количество результатов

### Поиск по коллекции:
1. Выберите "Поиск NFT"
2. Введите название коллекции (например: "Telegram Premium")
3. Оставьте остальные поля пустыми или заполните по желанию

### Поиск в ценовом диапазоне:
1. Выберите "Поиск по цене"
2. Введите минимальную цену (например: 1.0)
3. Введите максимальную цену (например: 10.0)

## 🔧 Решение проблем

### Ошибка "Токен не работает":
- Получите новый токен (токены имеют ограниченное время жизни)
- Убедитесь, что скопировали полное значение Authorization

### Ошибка "NFT не найдены":
- Попробуйте изменить параметры поиска
- Проверьте правильность названий коллекций/моделей

### Ошибка подключения:
- Проверьте интернет-соединение
- Убедитесь, что API Portals доступен

## 📁 Файлы проекта

- `bot_with_token.py` - **Основной бот** (рекомендуется)
- `main.py` - Бот с автоматической авторизацией
- `manual_test.py` - Тестирование API
- `portals_api.py` - API модуль
- `auth.py` - Модуль авторизации
- `config.py` - Конфигурация

## 💾 Сохранение токена

Бот автоматически сохраняет токен в файл `token.txt` для повторного использования.

---

**Готово!** 🎉 Теперь вы можете искать и мониторить NFT на Portals!
