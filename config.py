import os
from dotenv import load_dotenv

load_dotenv()

# Telegram API credentials - получить с https://my.telegram.org/auth
API_ID = int(os.getenv('API_ID', '22560657'))
API_HASH = os.getenv('API_HASH', '0e94404d3365f10e182ce552eeff0c12')
BOT_TOKEN = os.getenv('BOT_TOKEN', '7918026591:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI')

# Portals API
PORTALS_API_URL = 'https://portals-market.com'

# Настройки по умолчанию
DEFAULT_LIMIT = 20
DEFAULT_OFFSET = 0

# Доступные фильтры
AVAILABLE_COLLECTIONS = [
    'Telegram Premium',
    'Stars',
    'Durov',
    # Добавьте другие коллекции по необходимости
]

AVAILABLE_MODELS = [
    'Premium',
    'Star',
    'Durov',
    # Добавьте другие модели по необходимости
]

AVAILABLE_BACKDROPS = [
    'Black',
    'White',
    'Blue',
    'Red',
    'Green',
    # Добавьте другие фоны по необходимости
]

SORT_OPTIONS = {
    'listed_at': 'По времени выставления',
    'price': 'По цене',
    'external_collection_number': 'По номеру подарка',
    'model_rarity': 'По редкости модели'
}
