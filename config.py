import os
from dotenv import load_dotenv

load_dotenv()

# Telegram API credentials - получить с https://my.telegram.org/auth
API_ID = int(os.getenv('API_ID', '12123123'))  # Замените на ваш API_ID
API_HASH = os.getenv('API_HASH', 'fok2jg4h83okpglr')  # Замените на ваш API_HASH

# Portals API
PORTALS_API_URL = 'https://portals-market.com'

# Настройки по умолчанию
DEFAULT_LIMIT = 20
DEFAULT_OFFSET = 0

# Доступные фильтры
AVAILABLE_COLLECTIONS = [
    'Telegram Premium',
    'Stars',
    'Durov',
    # Добавьте другие коллекции по необходимости
]

AVAILABLE_MODELS = [
    'Premium',
    'Star',
    'Durov',
    # Добавьте другие модели по необходимости
]

AVAILABLE_BACKDROPS = [
    'Black',
    'White',
    'Blue',
    'Red',
    'Green',
    # Добавьте другие фоны по необходимости
]

SORT_OPTIONS = {
    'listed_at': 'По времени выставления',
    'price': 'По цене',
    'external_collection_number': 'По номеру подарка',
    'model_rarity': 'По редкости модели'
}
