#!/usr/bin/env python3
"""
NFT Portals Bot с поддержкой сохраненного токена
"""

import asyncio
import os
from curl_cffi import requests
from config import PORTALS_API_URL


class SimplePortalsAPI:
    def __init__(self, token=None):
        self.token = token
        self.base_url = PORTALS_API_URL
    
    def load_token(self):
        """Загружает токен из файла"""
        if os.path.exists('token.txt'):
            with open('token.txt', 'r') as f:
                self.token = f.read().strip()
                return True
        return False
    
    def save_token(self, token):
        """Сохраняет токен в файл"""
        with open('token.txt', 'w') as f:
            f.write(token)
        self.token = token
    
    def search_nfts(self, **kwargs):
        """Поиск NFT"""
        if not self.token:
            raise ValueError("Токен не установлен")
        
        headers = {'Authorization': self.token}
        
        # Строим параметры запроса
        params = []
        params.append(f"offset={kwargs.get('offset', 0)}")
        params.append(f"limit={kwargs.get('limit', 20)}")
        
        if 'filter_by_collections' in kwargs:
            params.append(f"filter_by_collections={kwargs['filter_by_collections']}")
        
        if 'filter_by_models' in kwargs:
            params.append(f"filter_by_models={kwargs['filter_by_models']}")
        
        if 'filter_by_backdrops' in kwargs:
            params.append(f"filter_by_backdrops={kwargs['filter_by_backdrops']}")
        
        if 'min_price' in kwargs:
            params.append(f"min_price={kwargs['min_price']}")
        
        if 'max_price' in kwargs:
            params.append(f"max_price={kwargs['max_price']}")
        
        if 'status' in kwargs:
            params.append(f"status={kwargs['status']}")
        
        if 'sort_by' in kwargs:
            sort_order = kwargs.get('sort_order', 'desc')
            params.append(f"sort_by={kwargs['sort_by']}+{sort_order}")
        
        query_string = '&'.join(params)
        url = f'{self.base_url}/nfts/search?{query_string}'
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Ошибка API: {e}")
            return None
    
    def format_nft(self, nft):
        """Форматирует информацию о NFT"""
        info = []
        info.append(f"🎁 NFT #{nft.get('external_collection_number', 'N/A')}")
        
        if 'collection' in nft:
            info.append(f"📦 Коллекция: {nft['collection']}")
        
        if 'model' in nft:
            info.append(f"🎨 Модель: {nft['model']}")
        
        if 'backdrop' in nft:
            info.append(f"🖼 Фон: {nft['backdrop']}")
        
        if 'price' in nft:
            info.append(f"💰 Цена: {nft['price']} TON")
        
        if 'model_rarity' in nft:
            info.append(f"⭐ Редкость: {nft['model_rarity']}")
        
        return '\n'.join(info)


class NFTBot:
    def __init__(self):
        self.api = SimplePortalsAPI()
        self.initialized = False
    
    def initialize(self):
        """Инициализация бота"""
        # Пытаемся загрузить сохраненный токен
        if self.api.load_token():
            print("✅ Токен загружен из файла")
            self.initialized = True
            return True
        
        # Если токена нет, просим ввести
        print("🔑 Токен не найден. Необходимо ввести токен.")
        print("\n📋 Инструкция по получению токена:")
        print("1. Откройте Telegram Web (web.telegram.org)")
        print("2. Нажмите F12 для открытия DevTools")
        print("3. Перейдите на вкладку Network")
        print("4. Откройте бота @portals и перейдите в Market")
        print("5. Найдите любой запрос к portals-market.com")
        print("6. В Headers найдите Authorization и скопируйте значение")
        
        token = input("\n🔑 Введите токен: ").strip()
        
        if token:
            self.api.save_token(token)
            print("💾 Токен сохранен")
            self.initialized = True
            return True
        
        return False
    
    def show_menu(self):
        """Показать меню"""
        print("\n" + "="*50)
        print("🤖 NFT PORTALS BOT")
        print("="*50)
        print("1. 🔍 Поиск NFT")
        print("2. 💰 Самые дешевые NFT")
        print("3. 💎 Самые дорогие NFT")
        print("4. 🆕 Последние NFT")
        print("5. 📊 Поиск по цене")
        print("6. 🔄 Обновить токен")
        print("0. ❌ Выход")
        print("="*50)
    
    def search_nfts(self):
        """Поиск NFT"""
        print("\n🔍 ПОИСК NFT")
        
        collection = input("Коллекция (или Enter): ").strip()
        model = input("Модель (или Enter): ").strip()
        backdrop = input("Фон (или Enter): ").strip()
        
        try:
            limit = int(input("Количество (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        params = {'limit': limit, 'status': 'listed'}
        if collection:
            params['filter_by_collections'] = collection
        if model:
            params['filter_by_models'] = model
        if backdrop:
            params['filter_by_backdrops'] = backdrop
        
        result = self.api.search_nfts(**params)
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Найдено {len(nfts)} NFT:")
            
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    def show_cheapest(self):
        """Самые дешевые NFT"""
        print("\n💰 САМЫЕ ДЕШЕВЫЕ NFT")
        
        try:
            limit = int(input("Количество (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        result = self.api.search_nfts(
            limit=limit,
            status='listed',
            sort_by='price',
            sort_order='asc'
        )
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Самые дешевые {len(nfts)} NFT:")
            
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    def show_most_expensive(self):
        """Самые дорогие NFT"""
        print("\n💎 САМЫЕ ДОРОГИЕ NFT")
        
        try:
            limit = int(input("Количество (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        result = self.api.search_nfts(
            limit=limit,
            status='listed',
            sort_by='price',
            sort_order='desc'
        )
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Самые дорогие {len(nfts)} NFT:")
            
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    def show_latest(self):
        """Последние NFT"""
        print("\n🆕 ПОСЛЕДНИЕ NFT")
        
        try:
            limit = int(input("Количество (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        result = self.api.search_nfts(
            limit=limit,
            status='listed',
            sort_by='listed_at',
            sort_order='desc'
        )
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Последние {len(nfts)} NFT:")
            
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    def search_by_price(self):
        """Поиск по цене"""
        print("\n📊 ПОИСК ПО ЦЕНЕ")
        
        try:
            min_price = float(input("Минимальная цена (TON): "))
            max_price = float(input("Максимальная цена (TON): "))
        except ValueError:
            print("❌ Неверный формат цены")
            return
        
        result = self.api.search_nfts(
            min_price=min_price,
            max_price=max_price,
            status='listed',
            sort_by='price',
            sort_order='asc'
        )
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Найдено {len(nfts)} NFT в диапазоне {min_price}-{max_price} TON:")
            
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT в указанном диапазоне не найдены")
    
    def update_token(self):
        """Обновить токен"""
        print("\n🔄 ОБНОВЛЕНИЕ ТОКЕНА")
        
        token = input("Введите новый токен: ").strip()
        
        if token:
            self.api.save_token(token)
            print("✅ Токен обновлен и сохранен")
        else:
            print("❌ Токен не введен")
    
    def run(self):
        """Запуск бота"""
        if not self.initialize():
            print("❌ Не удалось инициализировать бота")
            return
        
        while True:
            self.show_menu()
            choice = input("\nВыберите действие: ").strip()
            
            try:
                if choice == "1":
                    self.search_nfts()
                elif choice == "2":
                    self.show_cheapest()
                elif choice == "3":
                    self.show_most_expensive()
                elif choice == "4":
                    self.show_latest()
                elif choice == "5":
                    self.search_by_price()
                elif choice == "6":
                    self.update_token()
                elif choice == "0":
                    print("👋 До свидания!")
                    break
                else:
                    print("❌ Неверный выбор")
                
                if choice != "0":
                    input("\nНажмите Enter для продолжения...")
                    
            except Exception as e:
                print(f"❌ Произошла ошибка: {e}")
                input("\nНажмите Enter для продолжения...")


def main():
    bot = NFTBot()
    bot.run()


if __name__ == "__main__":
    main()
