import asyncio
from curl_cffi import requests
from typing import Dict, List, Optional, Union
from auth import PortalsAuth
from config import PORTALS_API_URL, DEFAULT_LIMIT, DEFAULT_OFFSET


class PortalsAPI:
    def __init__(self):
        self.auth = PortalsAuth()
        self.base_url = PORTALS_API_URL
        self.token = None
    
    async def initialize(self):
        """
        Инициализация API - получение токена авторизации
        """
        self.token = await self.auth.get_auth_token()
        if not self.token:
            raise Exception("Не удалось получить токен авторизации")
        return True
    
    def _build_search_url(self, **kwargs):
        """
        Строит URL для поиска NFT с параметрами
        """
        params = []
        
        # Основные параметры
        offset = kwargs.get('offset', DEFAULT_OFFSET)
        limit = kwargs.get('limit', DEFAULT_LIMIT)
        params.append(f'offset={offset}')
        params.append(f'limit={limit}')
        
        # Фильтры
        if 'filter_by_collections' in kwargs:
            collections = kwargs['filter_by_collections']
            if isinstance(collections, list):
                collections = ','.join(collections)
            params.append(f'filter_by_collections={collections}')
        
        if 'filter_by_models' in kwargs:
            models = kwargs['filter_by_models']
            if isinstance(models, list):
                models = ','.join(models)
            params.append(f'filter_by_models={models}')
        
        if 'filter_by_backdrops' in kwargs:
            backdrops = kwargs['filter_by_backdrops']
            if isinstance(backdrops, list):
                backdrops = ','.join(backdrops)
            params.append(f'filter_by_backdrops={backdrops}')
        
        if 'external_collection_number' in kwargs:
            params.append(f'external_collection_number={kwargs["external_collection_number"]}')
        
        if 'min_price' in kwargs:
            params.append(f'min_price={kwargs["min_price"]}')
        
        if 'max_price' in kwargs:
            params.append(f'max_price={kwargs["max_price"]}')
        
        if 'status' in kwargs:
            params.append(f'status={kwargs["status"]}')
        
        if 'sort_by' in kwargs:
            sort_by = kwargs['sort_by']
            sort_order = kwargs.get('sort_order', 'desc')
            params.append(f'sort_by={sort_by}+{sort_order}')
        
        query_string = '&'.join(params)
        return f'{self.base_url}/nfts/search?{query_string}'
    
    async def search_nfts(self, **kwargs) -> Dict:
        """
        Поиск NFT подарков
        
        Параметры:
        - filter_by_collections: список или строка с названиями коллекций
        - filter_by_models: список или строка с моделями
        - filter_by_backdrops: список или строка с фонами
        - external_collection_number: номер коллекции
        - min_price: минимальная цена
        - max_price: максимальная цена
        - limit: количество результатов (макс 100)
        - offset: смещение
        - status: 'listed'/'unlisted' или None для всех
        - sort_by: 'listed_at'/'price'/'external_collection_number'/'model_rarity'
        - sort_order: 'desc'/'asc'
        """
        if not self.token:
            await self.initialize()
        
        url = self._build_search_url(**kwargs)
        headers = self.auth.get_headers()
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Ошибка при поиске NFT: {e}")
            return None
    
    async def get_cheapest_nfts(self, limit: int = 10, **filters) -> List[Dict]:
        """
        Получить самые дешевые NFT
        """
        result = await self.search_nfts(
            sort_by='price',
            sort_order='asc',
            status='listed',
            limit=limit,
            **filters
        )
        return result.get('results', []) if result else []
    
    async def get_most_expensive_nfts(self, limit: int = 10, **filters) -> List[Dict]:
        """
        Получить самые дорогие NFT
        """
        result = await self.search_nfts(
            sort_by='price',
            sort_order='desc',
            status='listed',
            limit=limit,
            **filters
        )
        return result.get('results', []) if result else []
    
    async def get_latest_nfts(self, limit: int = 10, **filters) -> List[Dict]:
        """
        Получить последние выставленные NFT
        """
        result = await self.search_nfts(
            sort_by='listed_at',
            sort_order='desc',
            status='listed',
            limit=limit,
            **filters
        )
        return result.get('results', []) if result else []
    
    async def find_nfts_by_price_range(self, min_price: float, max_price: float, **filters) -> List[Dict]:
        """
        Найти NFT в определенном ценовом диапазоне
        """
        result = await self.search_nfts(
            min_price=min_price,
            max_price=max_price,
            status='listed',
            sort_by='price',
            sort_order='asc',
            **filters
        )
        return result.get('results', []) if result else []
    
    def format_nft_info(self, nft: Dict) -> str:
        """
        Форматирует информацию о NFT для вывода
        """
        info = []
        info.append(f"🎁 NFT #{nft.get('external_collection_number', 'N/A')}")
        
        if 'collection' in nft:
            info.append(f"📦 Коллекция: {nft['collection']}")
        
        if 'model' in nft:
            info.append(f"🎨 Модель: {nft['model']}")
        
        if 'backdrop' in nft:
            info.append(f"🖼 Фон: {nft['backdrop']}")
        
        if 'price' in nft:
            info.append(f"💰 Цена: {nft['price']} TON")
        
        if 'model_rarity' in nft:
            info.append(f"⭐ Редкость: {nft['model_rarity']}")
        
        if 'listed_at' in nft:
            info.append(f"📅 Выставлен: {nft['listed_at']}")
        
        return '\n'.join(info)


# Пример использования
async def example_usage():
    api = PortalsAPI()
    await api.initialize()
    
    # Поиск самых дешевых NFT с черным фоном
    cheap_nfts = await api.get_cheapest_nfts(
        limit=5,
        filter_by_backdrops='Black'
    )
    
    print("Самые дешевые NFT с черным фоном:")
    for nft in cheap_nfts:
        print(api.format_nft_info(nft))
        print("-" * 40)


if __name__ == "__main__":
    asyncio.run(example_usage())
