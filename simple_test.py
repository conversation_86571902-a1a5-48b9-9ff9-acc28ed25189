#!/usr/bin/env python3
"""
Простой тест для проверки работы с Portals API
"""

import asyncio
from urllib.parse import unquote
from curl_cffi import requests
from pyrogram import Client
from pyrogram.raw.functions.messages import RequestAppWebView
from pyrogram.raw.types import InputBotAppShortName, InputUser
from config import API_ID, API_HASH, PORTALS_API_URL


async def get_auth_token():
    """
    Получает токен авторизации для Portals API
    """
    client = Client('test_session', api_id=API_ID, api_hash=API_HASH)
    
    try:
        async with client:
            print("📱 Подключение к Telegram...")
            
            # Получаем информацию о боте Portals
            bot_entity = await client.get_users('portals')
            bot = InputUser(user_id=bot_entity.id, access_hash=bot_entity.raw.access_hash)
            peer = await client.resolve_peer('portals')
            
            print("🔗 Получение веб-приложения...")
            
            # Создаем запрос к веб-приложению
            bot_app = InputBotAppShortName(bot_id=bot, short_name='market')
            web_view = await client.invoke(
                RequestAppWebView(
                    peer=peer,
                    app=bot_app,
                    platform="android",
                )
            )
            
            # Извлекаем токен из URL
            init_data = unquote(
                web_view.url.split('tgWebAppData=', 1)[1].split('&tgWebAppVersion', 1)[0]
            )
            token = f'tma {init_data}'
            
            print("✅ Токен получен успешно!")
            return token
            
    except Exception as e:
        print(f"❌ Ошибка при получении токена: {e}")
        return None


async def test_api(token):
    """
    Тестирует API запрос
    """
    headers = {'Authorization': token}
    
    try:
        print("🔍 Выполняем тестовый запрос к API...")
        
        # Простой запрос для получения NFT
        url = f'{PORTALS_API_URL}/nfts/search?offset=0&limit=3&status=listed&sort_by=price+asc'
        
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        
        if 'results' in data:
            nfts = data['results']
            print(f"✅ API работает! Найдено {len(nfts)} NFT:")
            
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. NFT #{nft.get('external_collection_number', 'N/A')}")
                if 'collection' in nft:
                    print(f"   Коллекция: {nft['collection']}")
                if 'price' in nft:
                    print(f"   Цена: {nft['price']} TON")
                if 'model' in nft:
                    print(f"   Модель: {nft['model']}")
            
            return True
        else:
            print("❌ Неожиданный формат ответа API")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка API запроса: {e}")
        return False


async def main():
    """
    Главная функция
    """
    print("🤖 NFT Portals Bot - Простой тест")
    print("=" * 40)
    
    # Получаем токен
    token = await get_auth_token()
    
    if not token:
        print("❌ Не удалось получить токен")
        return
    
    # Тестируем API
    success = await test_api(token)
    
    if success:
        print("\n🎉 Тест прошел успешно!")
        print("✅ Бот готов к работе!")
    else:
        print("\n❌ Тест не прошел")


if __name__ == "__main__":
    asyncio.run(main())
