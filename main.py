import asyncio
import json
from portals_api import PortalsAPI
from config import AVAILABLE_COLLECTIONS, AVAILABLE_MODELS, AVAILABLE_BACKDROPS, SORT_OPTIONS


class NFTBot:
    def __init__(self):
        self.api = PortalsAPI()
        self.initialized = False
    
    async def initialize(self):
        """
        Инициализация бота
        """
        try:
            await self.api.initialize()
            self.initialized = True
            print("✅ NFT Bot успешно инициализирован!")
            return True
        except Exception as e:
            print(f"❌ Ошибка инициализации: {e}")
            return False
    
    def show_menu(self):
        """
        Показать главное меню
        """
        print("\n" + "="*50)
        print("🤖 NFT PORTALS BOT")
        print("="*50)
        print("1. 🔍 Поиск NFT")
        print("2. 💰 Самые дешевые NFT")
        print("3. 💎 Самые дорогие NFT")
        print("4. 🆕 Последние NFT")
        print("5. 📊 Поиск по цене")
        print("6. 🎯 Расширенный поиск")
        print("7. 📋 Показать доступные фильтры")
        print("8. 🔄 Мониторинг (каждые 30 сек)")
        print("0. ❌ Выход")
        print("="*50)
    
    def show_filters(self):
        """
        Показать доступные фильтры
        """
        print("\n📋 ДОСТУПНЫЕ ФИЛЬТРЫ:")
        print("\n🎨 Коллекции:")
        for i, collection in enumerate(AVAILABLE_COLLECTIONS, 1):
            print(f"  {i}. {collection}")
        
        print("\n🎭 Модели:")
        for i, model in enumerate(AVAILABLE_MODELS, 1):
            print(f"  {i}. {model}")
        
        print("\n🖼 Фоны:")
        for i, backdrop in enumerate(AVAILABLE_BACKDROPS, 1):
            print(f"  {i}. {backdrop}")
        
        print("\n📊 Сортировка:")
        for key, value in SORT_OPTIONS.items():
            print(f"  {key}: {value}")
    
    async def search_nfts(self):
        """
        Простой поиск NFT
        """
        print("\n🔍 ПОИСК NFT")
        
        # Получаем параметры поиска
        collection = input("Коллекция (или Enter для пропуска): ").strip()
        model = input("Модель (или Enter для пропуска): ").strip()
        backdrop = input("Фон (или Enter для пропуска): ").strip()
        
        try:
            limit = int(input("Количество результатов (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        # Формируем параметры
        params = {'limit': limit}
        if collection:
            params['filter_by_collections'] = collection
        if model:
            params['filter_by_models'] = model
        if backdrop:
            params['filter_by_backdrops'] = backdrop
        
        # Выполняем поиск
        result = await self.api.search_nfts(**params)
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Найдено {len(nfts)} NFT:")
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft_info(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены или произошла ошибка")
    
    async def show_cheapest(self):
        """
        Показать самые дешевые NFT
        """
        print("\n💰 САМЫЕ ДЕШЕВЫЕ NFT")
        
        try:
            limit = int(input("Количество результатов (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        nfts = await self.api.get_cheapest_nfts(limit=limit)
        
        if nfts:
            print(f"\n✅ Самые дешевые {len(nfts)} NFT:")
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft_info(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    async def show_most_expensive(self):
        """
        Показать самые дорогие NFT
        """
        print("\n💎 САМЫЕ ДОРОГИЕ NFT")
        
        try:
            limit = int(input("Количество результатов (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        nfts = await self.api.get_most_expensive_nfts(limit=limit)
        
        if nfts:
            print(f"\n✅ Самые дорогие {len(nfts)} NFT:")
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft_info(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    async def show_latest(self):
        """
        Показать последние NFT
        """
        print("\n🆕 ПОСЛЕДНИЕ NFT")
        
        try:
            limit = int(input("Количество результатов (по умолчанию 10): ") or "10")
        except ValueError:
            limit = 10
        
        nfts = await self.api.get_latest_nfts(limit=limit)
        
        if nfts:
            print(f"\n✅ Последние {len(nfts)} NFT:")
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft_info(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены")
    
    async def search_by_price(self):
        """
        Поиск по ценовому диапазону
        """
        print("\n📊 ПОИСК ПО ЦЕНЕ")
        
        try:
            min_price = float(input("Минимальная цена (TON): "))
            max_price = float(input("Максимальная цена (TON): "))
        except ValueError:
            print("❌ Неверный формат цены")
            return
        
        nfts = await self.api.find_nfts_by_price_range(min_price, max_price)
        
        if nfts:
            print(f"\n✅ Найдено {len(nfts)} NFT в диапазоне {min_price}-{max_price} TON:")
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft_info(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT в указанном ценовом диапазоне не найдены")
    
    async def advanced_search(self):
        """
        Расширенный поиск
        """
        print("\n🎯 РАСШИРЕННЫЙ ПОИСК")
        
        params = {}
        
        # Коллекции
        collections = input("Коллекции (через запятую, или Enter): ").strip()
        if collections:
            params['filter_by_collections'] = collections
        
        # Модели
        models = input("Модели (через запятую, или Enter): ").strip()
        if models:
            params['filter_by_models'] = models
        
        # Фоны
        backdrops = input("Фоны (через запятую, или Enter): ").strip()
        if backdrops:
            params['filter_by_backdrops'] = backdrops
        
        # Номер коллекции
        collection_number = input("Номер коллекции (или Enter): ").strip()
        if collection_number:
            try:
                params['external_collection_number'] = int(collection_number)
            except ValueError:
                print("⚠️ Неверный формат номера коллекции")
        
        # Цены
        min_price = input("Минимальная цена (или Enter): ").strip()
        if min_price:
            try:
                params['min_price'] = float(min_price)
            except ValueError:
                print("⚠️ Неверный формат минимальной цены")
        
        max_price = input("Максимальная цена (или Enter): ").strip()
        if max_price:
            try:
                params['max_price'] = float(max_price)
            except ValueError:
                print("⚠️ Неверный формат максимальной цены")
        
        # Статус
        status = input("Статус (listed/unlisted или Enter для всех): ").strip()
        if status in ['listed', 'unlisted']:
            params['status'] = status
        
        # Сортировка
        print("\nДоступные варианты сортировки:")
        for key, value in SORT_OPTIONS.items():
            print(f"  {key}: {value}")
        
        sort_by = input("Сортировка (или Enter для по умолчанию): ").strip()
        if sort_by in SORT_OPTIONS:
            params['sort_by'] = sort_by
            sort_order = input("Порядок (asc/desc, по умолчанию desc): ").strip()
            if sort_order in ['asc', 'desc']:
                params['sort_order'] = sort_order
        
        # Лимит
        try:
            limit = int(input("Количество результатов (по умолчанию 20): ") or "20")
            params['limit'] = limit
        except ValueError:
            params['limit'] = 20
        
        # Выполняем поиск
        result = await self.api.search_nfts(**params)
        
        if result and 'results' in result:
            nfts = result['results']
            print(f"\n✅ Найдено {len(nfts)} NFT:")
            for i, nft in enumerate(nfts, 1):
                print(f"\n{i}. {self.api.format_nft_info(nft)}")
                print("-" * 40)
        else:
            print("❌ NFT не найдены или произошла ошибка")
    
    async def monitor_nfts(self):
        """
        Мониторинг новых NFT
        """
        print("\n🔄 МОНИТОРИНГ NFT (каждые 30 секунд)")
        print("Нажмите Ctrl+C для остановки")
        
        last_nfts = set()
        
        try:
            while True:
                # Получаем последние NFT
                nfts = await self.api.get_latest_nfts(limit=5)
                
                if nfts:
                    current_nfts = set()
                    for nft in nfts:
                        nft_id = f"{nft.get('external_collection_number', '')}-{nft.get('collection', '')}"
                        current_nfts.add(nft_id)
                        
                        # Если это новый NFT
                        if nft_id not in last_nfts:
                            print(f"\n🆕 НОВЫЙ NFT ОБНАРУЖЕН!")
                            print(self.api.format_nft_info(nft))
                            print("-" * 40)
                    
                    last_nfts = current_nfts
                
                await asyncio.sleep(30)
                
        except KeyboardInterrupt:
            print("\n⏹ Мониторинг остановлен")
    
    async def run(self):
        """
        Запуск бота
        """
        if not await self.initialize():
            return
        
        while True:
            self.show_menu()
            choice = input("\nВыберите действие: ").strip()
            
            try:
                if choice == "1":
                    await self.search_nfts()
                elif choice == "2":
                    await self.show_cheapest()
                elif choice == "3":
                    await self.show_most_expensive()
                elif choice == "4":
                    await self.show_latest()
                elif choice == "5":
                    await self.search_by_price()
                elif choice == "6":
                    await self.advanced_search()
                elif choice == "7":
                    self.show_filters()
                elif choice == "8":
                    await self.monitor_nfts()
                elif choice == "0":
                    print("👋 До свидания!")
                    break
                else:
                    print("❌ Неверный выбор")
                
                if choice != "0":
                    input("\nНажмите Enter для продолжения...")
                    
            except Exception as e:
                print(f"❌ Произошла ошибка: {e}")
                input("\nНажмите Enter для продолжения...")


async def main():
    bot = NFTBot()
    await bot.run()


if __name__ == "__main__":
    asyncio.run(main())
