# NFT Portals Bot

Бот для работы с API Portals - поиск и мониторинг NFT подарков в Telegram.

## Возможности

- 🔍 Поиск NFT по различным параметрам
- 💰 Поиск самых дешевых NFT
- 💎 Поиск самых дорогих NFT
- 🆕 Просмотр последних выставленных NFT
- 📊 Поиск по ценовому диапазону
- 🎯 Расширенный поиск с множественными фильтрами
- 🔄 Мониторинг новых NFT в реальном времени

## Установка

1. Клонируйте репозиторий:
```bash
git clone <repository_url>
cd nft-bot
```

2. Установите зависимости:
```bash
pip install -r requirements.txt
```

3. Настройте конфигурацию:
   - Скопируйте `.env.example` в `.env`
   - Получите API_ID и API_HASH с https://my.telegram.org/auth
   - Заполните файл `.env`:

```env
API_ID=your_api_id
API_HASH=your_api_hash
```

## Использование

Запустите бота:
```bash
python main.py
```

### Доступные команды:

1. **Поиск NFT** - простой поиск по коллекции, модели и фону
2. **Самые дешевые NFT** - показывает NFT с минимальной ценой
3. **Самые дорогие NFT** - показывает NFT с максимальной ценой
4. **Последние NFT** - показывает недавно выставленные NFT
5. **Поиск по цене** - поиск в определенном ценовом диапазоне
6. **Расширенный поиск** - поиск с множественными фильтрами
7. **Показать фильтры** - список доступных коллекций, моделей и фонов
8. **Мониторинг** - отслеживание новых NFT каждые 30 секунд

## API Параметры

### Доступные фильтры:
- `filter_by_collections` - фильтр по коллекциям
- `filter_by_models` - фильтр по моделям
- `filter_by_backdrops` - фильтр по фонам
- `external_collection_number` - номер коллекции
- `min_price` / `max_price` - ценовой диапазон
- `status` - статус (listed/unlisted)
- `limit` - количество результатов (макс 100)
- `offset` - смещение для пагинации

### Сортировка:
- `listed_at` - по времени выставления (только desc)
- `price` - по цене (asc/desc)
- `external_collection_number` - по номеру (asc/desc)
- `model_rarity` - по редкости (asc/desc)

## Структура проекта

```
nft-bot/
├── main.py              # Основной файл бота
├── portals_api.py       # API для работы с Portals
├── auth.py              # Модуль аутентификации
├── config.py            # Конфигурация
├── requirements.txt     # Зависимости
├── .env.example         # Пример конфигурации
└── README.md           # Документация
```

## Примеры использования API

### Простой поиск:
```python
from portals_api import PortalsAPI

api = PortalsAPI()
await api.initialize()

# Поиск NFT с черным фоном
nfts = await api.search_nfts(filter_by_backdrops='Black', limit=10)
```

### Поиск самых дешевых:
```python
cheap_nfts = await api.get_cheapest_nfts(limit=5)
```

### Расширенный поиск:
```python
nfts = await api.search_nfts(
    filter_by_collections='Telegram Premium',
    filter_by_backdrops='Black,Blue',
    min_price=1.0,
    max_price=10.0,
    sort_by='price',
    sort_order='asc',
    limit=20
)
```

## Требования

- Python 3.7+
- Telegram API credentials (API_ID, API_HASH)
- Активная сессия Telegram

## Безопасность

- Никогда не делитесь своими API_ID и API_HASH
- Файл `.env` добавлен в `.gitignore`
- Токены авторизации обновляются автоматически

## Поддержка

При возникновении проблем:
1. Убедитесь, что API_ID и API_HASH указаны корректно
2. Проверьте подключение к интернету
3. Убедитесь, что у вас есть активная сессия Telegram

## Лицензия

MIT License
