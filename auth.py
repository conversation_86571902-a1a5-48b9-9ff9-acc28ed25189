import asyncio
from urllib.parse import unquote
from pyrogram import Client
from pyrogram.raw.functions.messages import RequestAppWebView
from pyrogram.raw.types import InputBotAppShortName, InputUser
from config import API_ID, API_HASH


class PortalsAuth:
    def __init__(self):
        self.client = Client('portals_session', api_id=API_ID, api_hash=API_HASH)
        self.token = None
    
    async def get_auth_token(self):
        """
        Получает токен авторизации для Portals API через Pyrogram
        """
        try:
            async with self.client:
                # Получаем информацию о боте Portals
                bot_entity = await self.client.get_users('portals')
                bot = InputUser(user_id=bot_entity.id, access_hash=bot_entity.raw.access_hash)
                peer = await self.client.resolve_peer('portals')
                
                # Создаем запрос к веб-приложению
                bot_app = InputBotAppShortName(bot_id=bot, short_name='market')
                web_view = await self.client.invoke(
                    RequestAppWebView(
                        peer=peer,
                        app=bot_app,
                        platform="android",
                    )
                )
                
                # Извлекаем токен из URL
                init_data = unquote(
                    web_view.url.split('tgWebAppData=', 1)[1].split('&tgWebAppVersion', 1)[0]
                )
                token = f'tma {init_data}'
                self.token = token
                return token
                
        except Exception as e:
            print(f"Ошибка при получении токена: {e}")
            return None
    
    def get_headers(self):
        """
        Возвращает заголовки для запросов к API
        """
        if not self.token:
            raise ValueError("Токен не получен. Сначала вызовите get_auth_token()")
        
        return {
            'Authorization': self.token,
            'Content-Type': 'application/json'
        }
    
    async def refresh_token(self):
        """
        Обновляет токен авторизации
        """
        return await self.get_auth_token()


# Функция для быстрого получения токена
async def get_portals_token():
    """
    Быстрая функция для получения токена
    """
    auth = PortalsAuth()
    return await auth.get_auth_token()


if __name__ == "__main__":
    # Тест получения токена
    async def test_auth():
        auth = PortalsAuth()
        token = await auth.get_auth_token()
        if token:
            print(f"Токен получен: {token[:50]}...")
        else:
            print("Не удалось получить токен")
    
    asyncio.run(test_auth())
