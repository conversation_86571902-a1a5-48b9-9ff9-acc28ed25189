#!/usr/bin/env python3
"""
Тестовый скрипт для проверки аутентификации с Portals API
"""

import asyncio
from auth import PortalsAuth
from portals_api import PortalsAPI


async def test_authentication():
    """
    Тестирует аутентификацию и базовый запрос к API
    """
    print("🔐 Тестирование аутентификации...")
    
    try:
        # Создаем объект аутентификации
        auth = PortalsAuth()
        
        # Получаем токен
        print("📱 Подключение к Telegram...")
        token = await auth.get_auth_token()
        
        if token:
            print(f"✅ Токен получен успешно: {token[:50]}...")
            
            # Тестируем API
            print("\n🔍 Тестирование API...")
            api = PortalsAPI()
            await api.initialize()
            
            # Простой запрос
            print("📊 Выполняем тестовый запрос...")
            result = await api.search_nfts(limit=3, status='listed')
            
            if result and 'results' in result:
                nfts = result['results']
                print(f"✅ API работает! Найдено {len(nfts)} NFT:")
                
                for i, nft in enumerate(nfts, 1):
                    print(f"\n{i}. {api.format_nft_info(nft)}")
                    print("-" * 30)
                
                return True
            else:
                print("❌ API запрос не вернул результатов")
                return False
                
        else:
            print("❌ Не удалось получить токен")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False


async def main():
    """
    Главная функция
    """
    print("🤖 NFT Portals Bot - Тест аутентификации")
    print("=" * 50)
    
    success = await test_authentication()
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("✅ Бот готов к работе!")
        print("\n💡 Запустите 'python main.py' для использования бота")
    else:
        print("\n❌ Тесты не прошли")
        print("🔧 Проверьте настройки в файле .env")


if __name__ == "__main__":
    asyncio.run(main())
