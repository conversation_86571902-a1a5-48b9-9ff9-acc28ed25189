#!/usr/bin/env python3
"""
Тест с ручным вводом токена
"""

import asyncio
from curl_cffi import requests
from config import PORTALS_API_URL


def test_api_with_token(token):
    """
    Тестирует API с готовым токеном
    """
    headers = {'Authorization': token}
    
    try:
        print("🔍 Выполняем тестовый запрос к API...")
        
        # Простой запрос для получения NFT
        url = f'{PORTALS_API_URL}/nfts/search?offset=0&limit=5&status=listed&sort_by=price+asc'
        
        response = requests.get(url, headers=headers)
        
        print(f"📊 Статус ответа: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if 'results' in data:
                nfts = data['results']
                print(f"✅ API работает! Найдено {len(nfts)} NFT:")
                
                for i, nft in enumerate(nfts, 1):
                    print(f"\n{i}. NFT #{nft.get('external_collection_number', 'N/A')}")
                    if 'collection' in nft:
                        print(f"   📦 Коллекция: {nft['collection']}")
                    if 'price' in nft:
                        print(f"   💰 Цена: {nft['price']} TON")
                    if 'model' in nft:
                        print(f"   🎨 Модель: {nft['model']}")
                    if 'backdrop' in nft:
                        print(f"   🖼 Фон: {nft['backdrop']}")
                
                return True
            else:
                print("❌ Неожиданный формат ответа API")
                print(f"Ответ: {data}")
                return False
        else:
            print(f"❌ Ошибка HTTP: {response.status_code}")
            print(f"Ответ: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка API запроса: {e}")
        return False


def main():
    """
    Главная функция
    """
    print("🤖 NFT Portals Bot - Тест с токеном")
    print("=" * 50)
    
    print("\n📋 Инструкция по получению токена:")
    print("1. Откройте Telegram Web (web.telegram.org)")
    print("2. Нажмите F12 для открытия DevTools")
    print("3. Перейдите на вкладку Network")
    print("4. Откройте бота @portals и перейдите в Market")
    print("5. Найдите любой запрос к portals-market.com")
    print("6. В Headers найдите Authorization и скопируйте значение")
    print("7. Вставьте токен ниже")
    
    print("\n" + "="*50)
    
    # Пример токена для демонстрации
    example_token = "tma query_id=AAH..."
    print(f"Пример токена: {example_token}")
    
    token = input("\n🔑 Введите ваш токен (или 'demo' для демо): ").strip()
    
    if token.lower() == 'demo':
        print("\n🎭 Демо режим - используем тестовый запрос без токена")
        # Тестовый запрос без авторизации (может не работать)
        test_api_with_token("")
    elif token:
        print(f"\n🔍 Тестируем с токеном: {token[:20]}...")
        success = test_api_with_token(token)
        
        if success:
            print("\n🎉 Тест прошел успешно!")
            print("✅ Токен работает!")
            
            # Сохраняем токен в файл для дальнейшего использования
            with open('token.txt', 'w') as f:
                f.write(token)
            print("💾 Токен сохранен в token.txt")
            
        else:
            print("\n❌ Тест не прошел")
            print("🔧 Проверьте правильность токена")
    else:
        print("❌ Токен не введен")


if __name__ == "__main__":
    main()
